import { NextResponse } from "next/server";
import { getCurrentSessionUser } from "@/server/auth/auth-session";
import { getVoiceModelRealtime, getVoiceModelTask } from "@/server/utils-media.server";
import { MediaResultStatus } from "@/@types/media/media-type";
import { OSS_URL_HOST, WEBNAME } from "@/lib/constants";
import { VoiceModelTask } from "@/server/db/schema.server";
import { handleApiError } from "@/@types/error-api";
import { VoiceModel } from "@/server/db/schema-media.server";

interface Params {
	id: string;
}

/**
 * 获取语音设计任务状态
 */
export async function POST(req: Request) {
	const params: Params = await req.json();
	if (!params.id) {
		return NextResponse.json({ status: 400, message: "The params is invalid." });
	}

	const sessionUser = await getCurrentSessionUser();
	if (!sessionUser) {
		return NextResponse.json({ status: 401, message: "Not authorized." });
	}
	const userId = sessionUser.id;
	if (process.env.NODE_ENV === "development") {
		console.log("params: ", params);
		console.log("userId: ", userId);
	}

	try {
		const voiceModelTask: VoiceModelTask | null = await getVoiceModelTask(params.id, userId);
		if (!voiceModelTask) {
			return NextResponse.json({ status: 404, message: "Generate task not found." });
		}
		if (voiceModelTask.status === MediaResultStatus.Completed) {
			const voiceModel: VoiceModel | null = await getVoiceModelRealtime(voiceModelTask.customVoiceId!, userId);
			if (!voiceModel) {
				return NextResponse.json({ status: 404, message: "Task result not found." });
			}
			return NextResponse.json({
				status: 200,
				taskStatus: voiceModelTask.status,
				voiceModel: {
					customVoiceId: voiceModel.customVoiceId,
					aiModelId: voiceModel.aiModelId,
					visibility: voiceModel.visibility,
					name: voiceModel.name,
					description: voiceModel.description,
					mediaPath: voiceModel.mediaPath ? `${OSS_URL_HOST}${voiceModel.mediaPath}` : null,
					prompt: voiceModel.prompt,
					textPreview: voiceModel.textPreview,
				},
			});
		}
		return NextResponse.json({
			status: 200,
			taskStatus: voiceModelTask.status,
			taskError: voiceModelTask.error,
		});
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/voice/design/status`);
	}
}
