import { getSessionUserId } from "@/server/auth/auth-session";
import { NextResponse } from "next/server";
import { getDB } from "@/server/db/db-client.server";
import { WEBNAME } from "@/lib/constants";
import { MediaResultStatus } from "@/@types/media/media-type";
import { checkUserModelCredit, updateUserCredit } from "@/server/utils-credits.server";
import { handleApiError } from "@/@types/error-api";
import { mixpanelTrackEvent } from "@/server/mixpanel.server";
import { EVENT_DESIGN_VOICE } from "@/lib/track-events";
import { voiceModelTaskSchema } from "@/server/db/schema.server";
import { getUUIDString } from "@/lib/utils";

type Params = {
	prompt: string;
	textPreview: string;
	name: string;
	visibility: string;
};

export async function POST(req: Request) {
	const cfIpCountryCode = req.headers.get("cf-ipcountry");
	const cfIp = req.headers.get("cf-connecting-ip");
	const params: Params = await req.json();
	if (!params.prompt || !params.textPreview || !params.name) {
		return NextResponse.json({ status: 400, message: "Required parameters are missing (prompt, textPreview, name)." });
	}

	try {
		const userId = await getSessionUserId();

		const { creditConsumes, membershipLevel } = await checkUserModelCredit(userId, {
			needCredits: 1,
		});

		if (process.env.NODE_ENV === "development") {
			console.log("params: ", params);
			console.log("creditConsumes: ", creditConsumes);
		}

		// track mixpanel event
		mixpanelTrackEvent(EVENT_DESIGN_VOICE, userId, {
			mp_country_code: cfIpCountryCode,
			membershipLevel: membershipLevel,
		});

		const customVoiceId = `voicedesi-${getUUIDString()}`;
		const requestId = getUUIDString(); // Generate a unique request ID

		// save to db
		const db = getDB();
		await db.insert(voiceModelTaskSchema).values({
			userId: userId,
			thirdRequestId: requestId,
			customVoiceId: customVoiceId,
			status: MediaResultStatus.InProgress,
			visibility: params.visibility === "public",
			aiModelId: 1, // Default AI model ID, you may want to make this configurable
			prompt: params.prompt,
			textPreview: params.textPreview,
			name: params.name,
			description: params.prompt || null,
			creditsSources: JSON.stringify(creditConsumes),
			ip: cfIp,
		});

		// 更新用户token
		await updateUserCredit(userId, creditConsumes, {
			remark: `Voice design task request id: ${requestId}.`,
		});

		return NextResponse.json({
			status: 200,
			message: "Success",
			task_status: MediaResultStatus.InProgress,
			request_id: requestId,
		});
	} catch (error: any) {
		return handleApiError(error, `${WEBNAME} - /api/v1/voice/design`);
	}
}
