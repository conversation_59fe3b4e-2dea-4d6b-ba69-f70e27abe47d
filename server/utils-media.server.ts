import { getDB } from "./db/db-client.server";
import { eq } from "drizzle-orm";
import superjson from "superjson";
import { voiceModelTaskSchema, VoiceModelTask } from "./db/schema.server";
import { getKVVoiceModelTask } from "@/lib/utils";
import { getValue, setValue } from "./kv/redis-upstash.server";
import { DURATION_2_HOUR } from "@/lib/constants";
import { VoiceModel, voiceModelSchema } from "./db/schema-media.server";

export async function getVoiceModelTask(requestId: string, userId: string | null): Promise<VoiceModelTask | null> {
	let voiceModelTask: VoiceModelTask | null = null;

	//先从kv中获取
	const cacheKey = getKVVoiceModelTask(requestId);
	const kvDataVoiceModelTask = (await getValue(cacheKey)) as any;
	if (kvDataVoiceModelTask) {
		try {
			voiceModelTask = superjson.deserialize(kvDataVoiceModelTask) as VoiceModelTask;
		} catch (error) {
			console.error("[getVoiceModelTask] parse media task data from redis error:", error);
		}
	}

	//再从db中获取
	if (!voiceModelTask) {
		const db = getDB();
		const [newVoiceModelTask]: VoiceModelTask[] = await db.select().from(voiceModelTaskSchema).where(eq(voiceModelTaskSchema.thirdRequestId, requestId));
		if (newVoiceModelTask) {
			await setValue(cacheKey, superjson.stringify(newVoiceModelTask), DURATION_2_HOUR);
			voiceModelTask = newVoiceModelTask;
		}
	}

	if (!voiceModelTask) {
		return null;
	}

	if (userId && voiceModelTask.userId !== userId) {
		return null;
	}

	return voiceModelTask;
}

export async function getVoiceModelRealtime(customVoiceId: string, userId: string | null): Promise<VoiceModel | null> {
	const db = getDB();
	const [voiceModel]: VoiceModel[] = await db.select().from(voiceModelSchema).where(eq(voiceModelSchema.customVoiceId, customVoiceId));
	if (!voiceModel) {
		return null;
	}

	if (userId && voiceModel.userId !== userId) {
		return null;
	}

	return voiceModel;
}
